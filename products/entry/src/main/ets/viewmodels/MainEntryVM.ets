import { AccountUtil, UserInfo } from 'commonlib';
import { FormCardJump, TabListItem } from '../types/Types';
import { HomePageBuilder } from 'home';
import { MinePageBuilder } from 'mine';
import { AppStorageV2 } from '@kit.ArkUI';

const TAG: string = 'MainEntryVM';

@ObservedV2
export class MainEntryVM {
  @Trace userInfo: UserInfo = AccountUtil.getUserInfo();
  @Trace formCardJump: FormCardJump = AppStorageV2.connect(FormCardJump, () => new FormCardJump())!;
  @Trace curIndex: number = 0;
  public tabList: TabListItem[] = [
    {
      label: '服务器',
      icon: $r('app.media.icon_server_off'),
      iconChecked: $r('app.media.icon_server_on'),
      component: wrapBuilder(HomePageBuilder),
    },
    {
      label: '设置',
      icon: $r('app.media.icon_mine_off'),
      iconChecked: $r('app.media.icon_mine_on'),
      component: wrapBuilder(MinePageBuilder),
    },
  ];
  private static _instance: MainEntryVM;

  public static get instance() {
    if (!MainEntryVM._instance) {
      MainEntryVM._instance = new MainEntryVM();
    }
    return MainEntryVM._instance;
  }
}
